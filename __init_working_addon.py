bl_info = {
    "name": "Apply Scale",
    "author": "Control",
    "version": (1, 0, 1),
    "blender": (3, 0, 0),
    "location": "3D Viewport",
    "description": "Shows a button to apply scale when object scale is not (1,1,1)",
    "category": "Object",
}

import bpy
from bpy.types import GizmoGroup

class ApplyScaleOperator(bpy.types.Operator):
    """Apply scale to selected object"""
    bl_idname = "object.apply_scale_addon"
    bl_label = "Apply Scale"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        try:
            obj = context.active_object
            if obj and obj.select_get():
                # Store current mode
                current_mode = context.mode
                
                # Switch to object mode if in edit mode
                if current_mode == 'EDIT_MESH':
                    bpy.ops.object.mode_set(mode='OBJECT')
                
                # Apply scale
                bpy.ops.object.transform_apply(location=False, rotation=False, scale=True)
                
                # Return to original mode
                if current_mode == 'EDIT_MESH':
                    bpy.ops.object.mode_set(mode='EDIT')
                
                self.report({'INFO'}, "Scale applied")
            else:
                self.report({'WARNING'}, "No object selected")
                
        except Exception as e:
            self.report({'ERROR'}, f"Failed to apply scale: {str(e)}")
            return {'CANCELLED'}
            
        return {'FINISHED'}

def has_non_uniform_scale(obj):
    """Check if object has non-uniform scale (not 1,1,1)"""
    if not obj:
        return False
    
    scale = obj.scale
    tolerance = 0.0001
    return not (abs(scale.x - 1.0) < tolerance and 
                abs(scale.y - 1.0) < tolerance and 
                abs(scale.z - 1.0) < tolerance)

class ApplyScaleGizmoGroup(GizmoGroup):
    """Gizmo group for the apply scale button"""
    bl_idname = "VIEW3D_GGT_apply_scale_working"
    bl_label = "Apply Scale Gizmo"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'WINDOW'
    bl_options = {'PERSISTENT'}

    @classmethod
    def poll(cls, context):
        """Only show gizmo when object has non-uniform scale"""
        try:
            obj = context.active_object
            return obj and obj.select_get() and has_non_uniform_scale(obj)
        except:
            return False

    def setup(self, context):
        """Setup the gizmo"""
        try:
            # Create a 2D button gizmo
            gz = self.gizmos.new("GIZMO_GT_button_2d")
            gz.draw_style = 'BOX'
            gz.color = (0.2, 0.4, 0.8)
            gz.alpha = 0.7
            gz.color_highlight = (0.3, 0.6, 1.0)
            gz.alpha_highlight = 0.8
            gz.scale_basis = 80

            # Set up the operator to call when clicked
            gz.target_set_operator("object.apply_scale_addon")

            self.gizmo = gz
        except Exception as e:
            print(f"Error setting up gizmo: {e}")

    def draw_prepare(self, context):
        """Position the gizmo in the viewport"""
        try:
            if not hasattr(self, 'gizmo') or self.gizmo is None:
                return
                
            region = context.region
            if not region:
                return

            width = region.width
            margin = 100
            x = width - margin
            y = margin

            self.gizmo.matrix_basis.translation = (x, y, 0)
        except Exception as e:
            print(f"Error positioning gizmo: {e}")

def register():
    """Register the addon"""
    try:
        bpy.utils.register_class(ApplyScaleOperator)
        bpy.utils.register_class(ApplyScaleGizmoGroup)
        print("Apply Scale Addon registered successfully")
    except Exception as e:
        print(f"Error registering Apply Scale Addon: {e}")

def unregister():
    """Unregister the addon"""
    try:
        bpy.utils.unregister_class(ApplyScaleGizmoGroup)
        bpy.utils.unregister_class(ApplyScaleOperator)
        print("Apply Scale Addon unregistered successfully")
    except Exception as e:
        print(f"Error unregistering Apply Scale Addon: {e}")

if __name__ == "__main__":
    register()
