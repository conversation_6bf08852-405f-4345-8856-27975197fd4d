# SPDX-FileCopyrightText: 2024 Control
# SPDX-License-Identifier: GPL-3.0-or-later

"""
Apply Scale Extension for Blender

Shows a button to apply scale when object scale is not (1,1,1)
"""

import bpy
from bpy.types import GizmoGroup
from bpy.app.handlers import persistent

# Global variables
gizmo_group_registered = False

class ApplyScaleOperator(bpy.types.Operator):
    """Apply scale to selected object"""
    bl_idname = "object.apply_scale_addon"
    bl_label = "Apply Scale"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            obj = context.active_object
            if obj and obj.select_get():
                # Store current mode
                current_mode = context.mode

                # Switch to object mode if in edit mode
                if current_mode == 'EDIT_MESH':
                    bpy.ops.object.mode_set(mode='OBJECT')

                # Apply scale
                bpy.ops.object.transform_apply(location=False, rotation=False, scale=True)

                # Return to original mode
                if current_mode == 'EDIT_MESH':
                    bpy.ops.object.mode_set(mode='EDIT')

                # Force update to check if gizmo should be hidden
                check_and_update_gizmo()

                self.report({'INFO'}, "Scale applied successfully")
            else:
                self.report({'WARNING'}, "No object selected")

        except Exception as e:
            self.report({'ERROR'}, f"Failed to apply scale: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}

def has_non_uniform_scale(obj):
    """Check if object has non-uniform scale (not 1,1,1)"""
    if not obj:
        return False

    scale = obj.scale
    tolerance = 0.0001
    return not (abs(scale.x - 1.0) < tolerance and
                abs(scale.y - 1.0) < tolerance and
                abs(scale.z - 1.0) < tolerance)

def should_show_gizmo(context):
    """Check if gizmo should be shown"""
    try:
        obj = context.active_object
        return obj and obj.select_get() and has_non_uniform_scale(obj)
    except (AttributeError, RuntimeError):
        return False

class ApplyScaleGizmoGroup(GizmoGroup):
    """Gizmo group for the apply scale button"""
    bl_idname = "VIEW3D_GGT_apply_scale"
    bl_label = "Apply Scale Gizmo"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'WINDOW'
    bl_options = {'PERSISTENT'}

    @classmethod
    def poll(cls, context):
        """Only show gizmo when object has non-uniform scale"""
        try:
            return should_show_gizmo(context)
        except Exception:
            return False

    def setup(self, context):
        """Setup the gizmo"""
        try:
            # Create a 2D button gizmo
            gz = self.gizmos.new("GIZMO_GT_button_2d")
            gz.draw_style = 'BOX'
            gz.color = (0.2, 0.4, 0.8)
            gz.alpha = 0.7
            gz.color_highlight = (0.3, 0.6, 1.0)
            gz.alpha_highlight = 0.8
            gz.scale_basis = 80  # size in pixels

            # Set up the operator to call when clicked
            gz.target_set_operator("object.apply_scale_addon")

            self.gizmo = gz
        except Exception as e:
            print(f"Error setting up gizmo: {e}")
            self.gizmo = None

    def draw_prepare(self, context):
        """Position the gizmo in the viewport"""
        try:
            if not hasattr(self, 'gizmo') or self.gizmo is None:
                return

            region = context.region
            if not region:
                return

            width = region.width

            # Position the gizmo in bottom-right corner (100px from edges)
            margin = 100
            x = width - margin
            y = margin

            self.gizmo.matrix_basis.translation = (x, y, 0)
        except Exception as e:
            print(f"Error positioning gizmo: {e}")

def check_and_update_gizmo():
    """Force update of gizmo visibility"""
    try:
        # Force a redraw to update gizmo visibility
        for area in bpy.context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
    except Exception:
        pass

@persistent
def selection_change_handler(_scene, _depsgraph):
    """Handler for selection changes"""
    check_and_update_gizmo()

@persistent
def frame_change_handler(_scene, _depsgraph):
    """Handler for frame changes to update gizmo state"""
    check_and_update_gizmo()

def register():
    """Register the addon"""
    global gizmo_group_registered

    try:
        bpy.utils.register_class(ApplyScaleOperator)
        bpy.utils.register_class(ApplyScaleGizmoGroup)

        # Add handlers
        if selection_change_handler not in bpy.app.handlers.depsgraph_update_post:
            bpy.app.handlers.depsgraph_update_post.append(selection_change_handler)

        if frame_change_handler not in bpy.app.handlers.frame_change_post:
            bpy.app.handlers.frame_change_post.append(frame_change_handler)

        gizmo_group_registered = True
        print("Apply Scale Extension registered successfully")

    except Exception as e:
        print(f"Error registering Apply Scale Extension: {e}")

def unregister():
    """Unregister the addon"""
    global gizmo_group_registered

    try:
        # Remove handlers
        if selection_change_handler in bpy.app.handlers.depsgraph_update_post:
            bpy.app.handlers.depsgraph_update_post.remove(selection_change_handler)

        if frame_change_handler in bpy.app.handlers.frame_change_post:
            bpy.app.handlers.frame_change_post.remove(frame_change_handler)

        # Unregister classes
        if gizmo_group_registered:
            bpy.utils.unregister_class(ApplyScaleGizmoGroup)
            bpy.utils.unregister_class(ApplyScaleOperator)
            gizmo_group_registered = False

        print("Apply Scale Extension unregistered successfully")

    except Exception as e:
        print(f"Error unregistering Apply Scale Extension: {e}")

if __name__ == "__main__":
    register()