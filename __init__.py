bl_info = {
    "name": "Apply Scale Addon",
    "author": "Your Name",
    "version": (1, 0, 0),
    "blender": (3, 0, 0),
    "location": "3D Viewport",
    "description": "Shows a button to apply scale when object scale is not (1,1,1)",
    "category": "Object",
}

import bpy
import gpu
from gpu_extras.batch import batch_for_shader
from bpy.app.handlers import persistent

# Global variables
button_handler = None
is_button_registered = False
button_hover = False
button_rect = None

class ApplyScaleOperator(bpy.types.Operator):
    """Apply scale to selected object"""
    bl_idname = "object.apply_scale_addon"
    bl_label = "Apply Scale"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        obj = context.active_object
        if obj and obj.select_get():
            # Store current mode
            current_mode = context.mode

            # Switch to object mode if in edit mode
            if current_mode == 'EDIT_MESH':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Apply scale
            bpy.ops.object.transform_apply(location=False, rotation=False, scale=True)

            # Return to original mode
            if current_mode == 'EDIT_MESH':
                bpy.ops.object.mode_set(mode='EDIT')

            # Force update to check if button should be hidden
            check_and_update_button()

        return {'FINISHED'}

def has_non_uniform_scale(obj):
    """Check if object has non-uniform scale (not 1,1,1)"""
    if not obj:
        return False

    scale = obj.scale
    tolerance = 0.0001
    return not (abs(scale.x - 1.0) < tolerance and
                abs(scale.y - 1.0) < tolerance and
                abs(scale.z - 1.0) < tolerance)

def get_viewport_region():
    """Get the 3D viewport region"""
    for area in bpy.context.screen.areas:
        if area.type == 'VIEW_3D':
            for region in area.regions:
                if region.type == 'WINDOW':
                    return area, region
    return None, None

def draw_button():
    """Draw the apply scale button in the viewport"""
    global button_hover, button_rect

    area, region = get_viewport_region()
    if not area or not region:
        return

    # Button properties
    button_size = 80
    margin = 100
    icon_size = 64

    # Calculate button position (bottom-right corner with margin)
    button_x = region.width - margin - button_size
    button_y = margin

    button_rect = (button_x, button_y, button_size, button_size)

    # Check if mouse is over button
    mouse_x = bpy.context.window_manager.mouse_x - region.x
    mouse_y = bpy.context.window_manager.mouse_y - region.y

    button_hover = (button_x <= mouse_x <= button_x + button_size and
                   button_y <= mouse_y <= button_y + button_size)

    # Set up GPU state
    gpu.state.blend_set('ALPHA')

    # Button colors
    if button_hover:
        button_color = (0.3, 0.6, 1.0, 0.8)  # Light blue when hovering
    else:
        button_color = (0.2, 0.4, 0.8, 0.7)  # Dark blue normal state

    # Draw button background
    vertices = [
        (button_x, button_y),
        (button_x + button_size, button_y),
        (button_x + button_size, button_y + button_size),
        (button_x, button_y + button_size)
    ]

    indices = [(0, 1, 2), (2, 3, 0)]

    shader = gpu.shader.from_builtin('UNIFORM_COLOR')
    batch = batch_for_shader(shader, 'TRIS', {"pos": vertices}, indices=indices)

    shader.bind()
    shader.uniform_float("color", button_color)
    batch.draw(shader)

    # Draw button border
    border_color = (1.0, 1.0, 1.0, 0.8)
    border_vertices = [
        (button_x, button_y),
        (button_x + button_size, button_y),
        (button_x + button_size, button_y + button_size),
        (button_x, button_y + button_size),
        (button_x, button_y)  # Close the loop
    ]

    border_batch = batch_for_shader(shader, 'LINE_STRIP', {"pos": border_vertices})
    shader.uniform_float("color", border_color)
    border_batch.draw(shader)

    # Draw icon (simple scale icon using lines)
    icon_margin = (button_size - icon_size) // 2
    icon_x = button_x + icon_margin
    icon_y = button_y + icon_margin

    # Draw a simple "scale" icon - arrows pointing outward
    icon_color = (1.0, 1.0, 1.0, 1.0)
    center_x = icon_x + icon_size // 2
    center_y = icon_y + icon_size // 2
    arrow_length = icon_size // 3

    # Horizontal arrow
    h_arrow_vertices = [
        (center_x - arrow_length, center_y),
        (center_x + arrow_length, center_y),
        (center_x + arrow_length - 8, center_y - 4),
        (center_x + arrow_length, center_y),
        (center_x + arrow_length - 8, center_y + 4),
        (center_x - arrow_length, center_y),
        (center_x - arrow_length + 8, center_y - 4),
        (center_x - arrow_length, center_y),
        (center_x - arrow_length + 8, center_y + 4)
    ]

    # Vertical arrow
    v_arrow_vertices = [
        (center_x, center_y - arrow_length),
        (center_x, center_y + arrow_length),
        (center_x - 4, center_y + arrow_length - 8),
        (center_x, center_y + arrow_length),
        (center_x + 4, center_y + arrow_length - 8),
        (center_x, center_y - arrow_length),
        (center_x - 4, center_y - arrow_length + 8),
        (center_x, center_y - arrow_length),
        (center_x + 4, center_y - arrow_length + 8)
    ]

    # Draw arrows
    h_batch = batch_for_shader(shader, 'LINE_STRIP', {"pos": h_arrow_vertices})
    v_batch = batch_for_shader(shader, 'LINE_STRIP', {"pos": v_arrow_vertices})

    shader.uniform_float("color", icon_color)
    h_batch.draw(shader)
    v_batch.draw(shader)

    # Reset GPU state
    gpu.state.blend_set('NONE')

def button_modal_handler(_context, event):
    """Handle mouse events for button interaction"""
    global button_rect

    if event.type == 'LEFTMOUSE' and event.value == 'PRESS':
        if button_rect:
            mouse_x = event.mouse_region_x
            mouse_y = event.mouse_region_y

            button_x, button_y, button_size, _ = button_rect

            if (button_x <= mouse_x <= button_x + button_size and
                button_y <= mouse_y <= button_y + button_size):
                # Button clicked
                bpy.ops.object.apply_scale_addon()
                return {'RUNNING_MODAL'}

    return {'PASS_THROUGH'}

def register_button():
    """Register the button drawing function"""
    global button_handler, is_button_registered

    if not is_button_registered:
        button_handler = bpy.types.SpaceView3D.draw_handler_add(
            draw_button, (), 'WINDOW', 'POST_PIXEL'
        )
        is_button_registered = True

def unregister_button():
    """Unregister the button drawing function"""
    global button_handler, is_button_registered

    if is_button_registered and button_handler:
        bpy.types.SpaceView3D.draw_handler_remove(button_handler, 'WINDOW')
        button_handler = None
        is_button_registered = False

def check_and_update_button():
    """Check if button should be shown and update accordingly"""
    area, region = get_viewport_region()

    # If no viewport, unregister button
    if not area or not region:
        unregister_button()
        return

    # Check if there's an active object with non-uniform scale
    obj = bpy.context.active_object
    if obj and obj.select_get() and has_non_uniform_scale(obj):
        register_button()
    else:
        unregister_button()

@persistent
def selection_change_handler(scene, depsgraph):
    """Handler for selection changes"""
    check_and_update_button()

@persistent
def frame_change_handler(scene, depsgraph):
    """Handler for frame changes to update button state"""
    check_and_update_button()

class ApplyScaleModalOperator(bpy.types.Operator):
    """Modal operator to handle button clicks"""
    bl_idname = "view3d.apply_scale_modal"
    bl_label = "Apply Scale Modal"

    def modal(self, context, event):
        return button_modal_handler(context, event)

    def invoke(self, context, event):
        context.window_manager.modal_handler_add(self)
        return {'RUNNING_MODAL'}

def register():
    """Register the addon"""
    bpy.utils.register_class(ApplyScaleOperator)
    bpy.utils.register_class(ApplyScaleModalOperator)

    # Add handlers
    if selection_change_handler not in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.append(selection_change_handler)

    if frame_change_handler not in bpy.app.handlers.frame_change_post:
        bpy.app.handlers.frame_change_post.append(frame_change_handler)

    # Start modal operator for button interaction
    bpy.ops.view3d.apply_scale_modal('INVOKE_DEFAULT')

    # Initial check
    check_and_update_button()

def unregister():
    """Unregister the addon"""
    # Remove handlers
    if selection_change_handler in bpy.app.handlers.depsgraph_update_post:
        bpy.app.handlers.depsgraph_update_post.remove(selection_change_handler)

    if frame_change_handler in bpy.app.handlers.frame_change_post:
        bpy.app.handlers.frame_change_post.remove(frame_change_handler)

    # Unregister button
    unregister_button()

    # Unregister classes
    bpy.utils.unregister_class(ApplyScaleModalOperator)
    bpy.utils.unregister_class(ApplyScaleOperator)

if __name__ == "__main__":
    register()