# SPDX-FileCopyrightText: 2024 Control
# SPDX-License-Identifier: GPL-3.0-or-later

"""
Apply Scale Extension for Blender - Panel Version

Shows a button to apply scale when object scale is not (1,1,1)
"""

import bpy

# For backward compatibility with older Blender versions
if bpy.app.version < (4, 2, 0):
    bl_info = {
        "name": "Apply Scale",
        "author": "Control",
        "version": (1, 0, 1),
        "blender": (3, 0, 0),
        "location": "3D Viewport",
        "description": "Shows a button to apply scale when object scale is not (1,1,1)",
        "category": "Object",
    }

class ApplyScaleOperator(bpy.types.Operator):
    """Apply scale to selected object"""
    bl_idname = "object.apply_scale_addon"
    bl_label = "Apply Scale"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            obj = context.active_object
            if obj and obj.select_get():
                # Store current mode
                current_mode = context.mode

                # Switch to object mode if in edit mode
                if current_mode == 'EDIT_MESH':
                    bpy.ops.object.mode_set(mode='OBJECT')

                # Apply scale
                bpy.ops.object.transform_apply(location=False, rotation=False, scale=True)

                # Return to original mode
                if current_mode == 'EDIT_MESH':
                    bpy.ops.object.mode_set(mode='EDIT')

                self.report({'INFO'}, "Scale applied successfully")
            else:
                self.report({'WARNING'}, "No object selected")

        except Exception as e:
            self.report({'ERROR'}, f"Failed to apply scale: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}

def has_non_uniform_scale(obj):
    """Check if object has non-uniform scale (not 1,1,1)"""
    if not obj:
        return False

    scale = obj.scale
    tolerance = 0.0001
    return not (abs(scale.x - 1.0) < tolerance and
                abs(scale.y - 1.0) < tolerance and
                abs(scale.z - 1.0) < tolerance)

class VIEW3D_PT_apply_scale(bpy.types.Panel):
    """Panel that shows apply scale button"""
    bl_label = "Apply Scale"
    bl_idname = "VIEW3D_PT_apply_scale"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "Tool"

    @classmethod
    def poll(cls, context):
        """Only show panel when object has non-uniform scale"""
        try:
            obj = context.active_object
            return obj and obj.select_get() and has_non_uniform_scale(obj)
        except:
            return False

    def draw(self, context):
        layout = self.layout
        layout.operator("object.apply_scale_addon", text="Apply Scale", icon='OBJECT_ORIGIN')

def register():
    """Register the extension"""
    try:
        bpy.utils.register_class(ApplyScaleOperator)
        bpy.utils.register_class(VIEW3D_PT_apply_scale)
        print("Apply Scale Extension registered successfully")
    except Exception as e:
        print(f"Error registering Apply Scale Extension: {e}")

def unregister():
    """Unregister the extension"""
    try:
        bpy.utils.unregister_class(VIEW3D_PT_apply_scale)
        bpy.utils.unregister_class(ApplyScaleOperator)
        print("Apply Scale Extension unregistered successfully")
    except Exception as e:
        print(f"Error unregistering Apply Scale Extension: {e}")

if __name__ == "__main__":
    register()