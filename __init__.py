# SPDX-FileCopyrightText: 2024 Control
# SPDX-License-Identifier: GPL-3.0-or-later

"""
Apply Scale Extension for Blender - Panel Version

Shows a button to apply scale when object scale is not (1,1,1)
"""

import bpy
from bpy.props import FloatVectorProperty, IntProperty, EnumProperty
from bpy.types import AddonPreferences

# For backward compatibility with older Blender versions
if bpy.app.version < (4, 2, 0):
    bl_info = {
        "name": "Apply Scale",
        "author": "Control",
        "version": (1, 0, 1),
        "blender": (3, 0, 0),
        "location": "3D Viewport",
        "description": "Shows a button to apply scale when object scale is not (1,1,1)",
        "category": "Object",
    }

class ApplyScaleOperator(bpy.types.Operator):
    """Apply scale to selected object"""
    bl_idname = "object.apply_scale_addon"
    bl_label = "Apply Scale"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            selected_objects = context.selected_objects
            if not selected_objects:
                self.report({'WARNING'}, "No objects selected")
                return {'CANCELLED'}

            # Store current mode
            current_mode = context.mode

            # Switch to object mode if in edit mode
            if current_mode == 'EDIT_MESH':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Count objects that need scale applied
            objects_to_apply = []
            for obj in selected_objects:
                if has_non_uniform_scale(obj):
                    objects_to_apply.append(obj)

            if not objects_to_apply:
                self.report({'INFO'}, "No objects with non-uniform scale found")
                return {'FINISHED'}

            # Clear selection and apply scale to each object individually
            bpy.ops.object.select_all(action='DESELECT')

            applied_count = 0
            for obj in objects_to_apply:
                try:
                    # Select only this object
                    obj.select_set(True)
                    context.view_layer.objects.active = obj

                    # Apply scale to this object
                    bpy.ops.object.transform_apply(location=False, rotation=False, scale=True)

                    # Deselect for next iteration
                    obj.select_set(False)
                    applied_count += 1

                except Exception as obj_error:
                    self.report({'WARNING'}, f"Failed to apply scale to {obj.name}: {str(obj_error)}")

            # Restore original selection
            for obj in selected_objects:
                obj.select_set(True)

            # Return to original mode
            if current_mode == 'EDIT_MESH':
                bpy.ops.object.mode_set(mode='EDIT')

            if applied_count > 0:
                self.report({'INFO'}, f"Scale applied to {applied_count} object(s)")
            else:
                self.report({'WARNING'}, "No scales were applied")

        except Exception as e:
            self.report({'ERROR'}, f"Failed to apply scale: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}

class ApplyScaleAddonPreferences(AddonPreferences):
    """Preferences for Apply Scale Addon"""
    bl_idname = __name__

    # Gizmo size
    gizmo_size: IntProperty(
        name="Gizmo Size",
        description="Size of the apply scale button in pixels",
        default=48,
        min=16,
        max=256
    )

    # Gizmo margin X from viewport edge
    gizmo_margin_x: IntProperty(
        name="Margin X",
        description="Distance from viewport left/right edge in pixels (0 = aligned to edge)",
        default=12,
        min=0,
        max=500
    )

    # Gizmo margin Y from viewport edge
    gizmo_margin_y: IntProperty(
        name="Margin Y",
        description="Distance from viewport top/bottom edge in pixels (0 = aligned to edge)",
        default=12,
        min=0,
        max=500
    )

    # Gizmo base color (RGBA)
    gizmo_color: FloatVectorProperty(
        name="Base Color",
        description="Base color of the gizmo (RGBA)",
        subtype='COLOR',
        size=4,
        default=(0.547, 0.42, 0.0, 0.7),
        min=0.0,
        max=1.0
    )

    # Gizmo highlight color (RGBA)
    gizmo_highlight_color: FloatVectorProperty(
        name="Highlight Color",
        description="Highlight color when hovering (RGBA)",
        subtype='COLOR',
        size=4,
        default=(0.643, 0.493, 0.0, 0.9),
        min=0.0,
        max=1.0
    )

    # Corner alignment
    gizmo_corner: EnumProperty(
        name="Corner Alignment",
        description="Which corner of the viewport to align the gizmo to",
        items=[
            ('BOTTOM_RIGHT', "Bottom Right", "Position gizmo in bottom-right corner"),
            ('BOTTOM_LEFT', "Bottom Left", "Position gizmo in bottom-left corner"),
            ('TOP_RIGHT', "Top Right", "Position gizmo in top-right corner"),
            ('TOP_LEFT', "Top Left", "Position gizmo in top-left corner"),
        ],
        default='BOTTOM_RIGHT'
    )

    def draw(self, context):
        layout = self.layout

        # Gizmo Settings
        box = layout.box()
        box.label(text="Gizmo Appearance:", icon='SETTINGS')

        col = box.column()
        col.prop(self, "gizmo_size")

        # Margin settings
        row = col.row()
        row.prop(self, "gizmo_margin_x")
        row.prop(self, "gizmo_margin_y")
        col.separator()

        col.prop(self, "gizmo_color")
        col.prop(self, "gizmo_highlight_color")
        col.separator()

        col.prop(self, "gizmo_corner")

def has_non_uniform_scale(obj):
    """Check if object has non-uniform scale (not 1,1,1)"""
    if not obj:
        return False

    scale = obj.scale
    tolerance = 0.0001
    return not (abs(scale.x - 1.0) < tolerance and
                abs(scale.y - 1.0) < tolerance and
                abs(scale.z - 1.0) < tolerance)

from bpy.types import GizmoGroup

class ApplyScaleGizmoGroup(GizmoGroup):
    """Gizmo group for the apply scale button"""
    bl_idname = "VIEW3D_GGT_apply_scale_working"
    bl_label = "Apply Scale Gizmo"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'WINDOW'
    bl_options = {'PERSISTENT', 'SCALE'}

    @classmethod
    def poll(cls, context):
        """Only show gizmo when any selected object has non-uniform scale"""
        try:
            # Check all selected objects, not just the active one
            selected_objects = context.selected_objects
            if not selected_objects:
                return False

            # Return True if any selected object has non-uniform scale
            for obj in selected_objects:
                if has_non_uniform_scale(obj):
                    return True
            return False
        except:
            return False

    def setup(self, context):
        """Setup the gizmo"""
        try:
            # Get addon preferences
            addon_prefs = context.preferences.addons[__name__].preferences

            # Create a 2D button gizmo using the working pattern
            gz = self.gizmos.new("GIZMO_GT_button_2d")
            gz.show_drag = False
            gz.icon = 'FULLSCREEN_ENTER'  # Changed to fullscreen enter icon
            gz.draw_options = {'BACKDROP', 'OUTLINE'}

            # Set colors from preferences
            gz.color = addon_prefs.gizmo_color[:3]  # RGB only
            gz.alpha = addon_prefs.gizmo_color[3]   # Alpha from 4th component
            gz.color_highlight = addon_prefs.gizmo_highlight_color[:3]  # RGB only
            gz.alpha_highlight = addon_prefs.gizmo_highlight_color[3]   # Alpha from 4th component

            # Set size from preferences - scale_basis controls the overall gizmo size
            # For button_2d gizmos, this affects both the backdrop and icon size
            gz.scale_basis = addon_prefs.gizmo_size / 2  # Divide by 2 for proper sizing

            # Set up the operator to call when clicked
            gz.target_set_operator("object.apply_scale_addon")

            self.apply_scale_gizmo = gz
            #print(f"Gizmo setup successful - Size: {addon_prefs.gizmo_size}px, Corner: {addon_prefs.gizmo_corner}")
        except Exception as e:
            #print(f"Error setting up gizmo: {e}")
            self.apply_scale_gizmo = None

    def draw_prepare(self, context):
        """Position the gizmo in the viewport"""
        try:
            if not hasattr(self, 'apply_scale_gizmo') or self.apply_scale_gizmo is None:
                return

            region = context.region
            if not region:
                return

            # Get addon preferences
            addon_prefs = context.preferences.addons[__name__].preferences
            margin_x = addon_prefs.gizmo_margin_x
            margin_y = addon_prefs.gizmo_margin_y
            corner = addon_prefs.gizmo_corner

            # For button_2d gizmos, this affects both the backdrop and icon size
            self.apply_scale_gizmo.scale_basis = addon_prefs.gizmo_size / 2  # Divide by 2 for proper sizing

            # Set colors from preferences
            self.apply_scale_gizmo.color = addon_prefs.gizmo_color[:3]  # RGB only
            self.apply_scale_gizmo.alpha = addon_prefs.gizmo_color[3]   # Alpha from 4th component
            self.apply_scale_gizmo.color_highlight = addon_prefs.gizmo_highlight_color[:3]  # RGB only
            self.apply_scale_gizmo.alpha_highlight = addon_prefs.gizmo_highlight_color[3]   # Alpha from 4th component


            # Calculate position based on corner alignment
            # Add half gizmo size to position from edge rather than center
            half_size = addon_prefs.gizmo_size / 2

            if corner == 'BOTTOM_RIGHT':
                x = region.width - margin_x - half_size if margin_x > 0 else region.width - half_size
                y = margin_y + half_size
            elif corner == 'BOTTOM_LEFT':
                x = margin_x + half_size
                y = margin_y + half_size
            elif corner == 'TOP_RIGHT':
                x = region.width - margin_x - half_size if margin_x > 0 else region.width - half_size
                y = region.height - margin_y - half_size if margin_y > 0 else region.height - half_size
            elif corner == 'TOP_LEFT':
                x = margin_x + half_size
                y = region.height - margin_y - half_size if margin_y > 0 else region.height - half_size
            else:  # Default to bottom-right
                x = region.width - margin_x - half_size if margin_x > 0 else region.width - half_size
                y = margin_y + half_size

            # Set position using matrix_basis
            self.apply_scale_gizmo.matrix_basis[0][3] = x
            self.apply_scale_gizmo.matrix_basis[1][3] = y

        except Exception as e:
            print(f"Error positioning gizmo: {e}")

def register():
    """Register the extension"""
    try:
        bpy.utils.register_class(ApplyScaleOperator)
        bpy.utils.register_class(ApplyScaleAddonPreferences)
        bpy.utils.register_class(ApplyScaleGizmoGroup)
        print("Apply Scale Extension registered successfully")
    except Exception as e:
        print(f"Error registering Apply Scale Extension: {e}")

def unregister():
    """Unregister the extension"""
    try:
        bpy.utils.unregister_class(ApplyScaleGizmoGroup)
        bpy.utils.unregister_class(ApplyScaleAddonPreferences)
        bpy.utils.unregister_class(ApplyScaleOperator)
        print("Apply Scale Extension unregistered successfully")
    except Exception as e:
        print(f"Error unregistering Apply Scale Extension: {e}")

if __name__ == "__main__":
    register()