# SPDX-FileCopyrightText: 2024 Control
# SPDX-License-Identifier: GPL-3.0-or-later

"""
Apply Scale Extension for Blender - Panel Version

Shows a button to apply scale when object scale is not (1,1,1)
"""

import bpy
from bpy.props import FloatVectorProperty, IntProperty, EnumProperty
from bpy.types import AddonPreferences

# For backward compatibility with older Blender versions
if bpy.app.version < (4, 2, 0):
    bl_info = {
        "name": "Apply Scale",
        "author": "Control",
        "version": (1, 0, 1),
        "blender": (3, 0, 0),
        "location": "3D Viewport",
        "description": "Shows a button to apply scale when object scale is not (1,1,1)",
        "category": "Object",
    }

class ApplyScaleOperator(bpy.types.Operator):
    """Apply scale to selected object"""
    bl_idname = "object.apply_scale_addon"
    bl_label = "Apply Scale"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        try:
            obj = context.active_object
            if obj and obj.select_get():
                # Store current mode
                current_mode = context.mode

                # Switch to object mode if in edit mode
                if current_mode == 'EDIT_MESH':
                    bpy.ops.object.mode_set(mode='OBJECT')

                # Apply scale
                bpy.ops.object.transform_apply(location=False, rotation=False, scale=True)

                # Return to original mode
                if current_mode == 'EDIT_MESH':
                    bpy.ops.object.mode_set(mode='EDIT')

                self.report({'INFO'}, "Scale applied successfully")
            else:
                self.report({'WARNING'}, "No object selected")

        except Exception as e:
            self.report({'ERROR'}, f"Failed to apply scale: {str(e)}")
            return {'CANCELLED'}

        return {'FINISHED'}

class ApplyScaleAddonPreferences(AddonPreferences):
    """Preferences for Apply Scale Addon"""
    bl_idname = __name__

    # Gizmo size
    gizmo_size: IntProperty(
        name="Gizmo Size",
        description="Size of the apply scale button in pixels",
        default=40,
        min=20,
        max=100
    )

    # Gizmo margin from viewport edge
    gizmo_margin: IntProperty(
        name="Margin",
        description="Distance from viewport edge in pixels",
        default=100,
        min=10,
        max=300
    )

    # Gizmo base color (RGBA)
    gizmo_color: FloatVectorProperty(
        name="Base Color",
        description="Base color of the gizmo (RGBA)",
        subtype='COLOR',
        size=4,
        default=(0.2, 0.4, 0.8, 0.7),
        min=0.0,
        max=1.0
    )

    # Gizmo highlight color (RGBA)
    gizmo_highlight_color: FloatVectorProperty(
        name="Highlight Color",
        description="Highlight color when hovering (RGBA)",
        subtype='COLOR',
        size=4,
        default=(0.3, 0.6, 1.0, 0.8),
        min=0.0,
        max=1.0
    )

    # Corner alignment
    gizmo_corner: EnumProperty(
        name="Corner Alignment",
        description="Which corner of the viewport to align the gizmo to",
        items=[
            ('BOTTOM_RIGHT', "Bottom Right", "Position gizmo in bottom-right corner"),
            ('BOTTOM_LEFT', "Bottom Left", "Position gizmo in bottom-left corner"),
            ('TOP_RIGHT', "Top Right", "Position gizmo in top-right corner"),
            ('TOP_LEFT', "Top Left", "Position gizmo in top-left corner"),
        ],
        default='BOTTOM_RIGHT'
    )

    def draw(self, context):
        layout = self.layout

        # Gizmo Settings
        box = layout.box()
        box.label(text="Gizmo Appearance:", icon='SETTINGS')

        col = box.column()
        col.prop(self, "gizmo_size")
        col.prop(self, "gizmo_margin")
        col.separator()

        col.prop(self, "gizmo_color")
        col.prop(self, "gizmo_highlight_color")
        col.separator()

        col.prop(self, "gizmo_corner")

        # Preview info
        box = layout.box()
        box.label(text="Preview:", icon='INFO')
        col = box.column()
        col.label(text=f"Size: {self.gizmo_size}px")
        col.label(text=f"Margin: {self.gizmo_margin}px from edge")
        col.label(text=f"Position: {self.gizmo_corner.replace('_', ' ').title()}")
        col.label(text=f"Base Color: RGBA({self.gizmo_color[0]:.2f}, {self.gizmo_color[1]:.2f}, {self.gizmo_color[2]:.2f}, {self.gizmo_color[3]:.2f})")
        col.label(text=f"Highlight: RGBA({self.gizmo_highlight_color[0]:.2f}, {self.gizmo_highlight_color[1]:.2f}, {self.gizmo_highlight_color[2]:.2f}, {self.gizmo_highlight_color[3]:.2f})")

def has_non_uniform_scale(obj):
    """Check if object has non-uniform scale (not 1,1,1)"""
    if not obj:
        return False

    scale = obj.scale
    tolerance = 0.0001
    return not (abs(scale.x - 1.0) < tolerance and
                abs(scale.y - 1.0) < tolerance and
                abs(scale.z - 1.0) < tolerance)

from bpy.types import GizmoGroup

class ApplyScaleGizmoGroup(GizmoGroup):
    """Gizmo group for the apply scale button"""
    bl_idname = "VIEW3D_GGT_apply_scale_working"
    bl_label = "Apply Scale Gizmo"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'WINDOW'
    bl_options = {'PERSISTENT', 'SCALE'}

    @classmethod
    def poll(cls, context):
        """Only show gizmo when object has non-uniform scale"""
        try:
            obj = context.active_object
            return obj and obj.select_get() and has_non_uniform_scale(obj)
        except:
            return False

    def setup(self, context):
        """Setup the gizmo"""
        try:
            # Get addon preferences
            addon_prefs = context.preferences.addons[__name__].preferences

            # Create a 2D button gizmo using the working pattern
            gz = self.gizmos.new("GIZMO_GT_button_2d")
            gz.show_drag = False
            gz.icon = 'OBJECT_ORIGIN'  # Use icon instead of draw_style
            gz.draw_options = {'BACKDROP', 'OUTLINE'}

            # Set colors from preferences
            gz.color = addon_prefs.gizmo_color[:3]  # RGB only
            gz.alpha = addon_prefs.gizmo_color[3]   # Alpha from 4th component
            gz.color_highlight = addon_prefs.gizmo_highlight_color[:3]  # RGB only
            gz.alpha_highlight = addon_prefs.gizmo_highlight_color[3]   # Alpha from 4th component

            # Set size from preferences
            gz.scale_basis = addon_prefs.gizmo_size

            # Set up the operator to call when clicked
            gz.target_set_operator("object.apply_scale_addon")

            self.apply_scale_gizmo = gz
            print(f"Gizmo setup successful - Size: {addon_prefs.gizmo_size}px, Corner: {addon_prefs.gizmo_corner}")
        except Exception as e:
            print(f"Error setting up gizmo: {e}")
            self.apply_scale_gizmo = None

    def draw_prepare(self, context):
        """Position the gizmo in the viewport"""
        try:
            if not hasattr(self, 'apply_scale_gizmo') or self.apply_scale_gizmo is None:
                return

            region = context.region
            if not region:
                return

            # Get addon preferences
            addon_prefs = context.preferences.addons[__name__].preferences
            margin = addon_prefs.gizmo_margin
            corner = addon_prefs.gizmo_corner

            # Calculate position based on corner alignment
            if corner == 'BOTTOM_RIGHT':
                x = region.width - margin
                y = margin
            elif corner == 'BOTTOM_LEFT':
                x = margin
                y = margin
            elif corner == 'TOP_RIGHT':
                x = region.width - margin
                y = region.height - margin
            elif corner == 'TOP_LEFT':
                x = margin
                y = region.height - margin
            else:  # Default to bottom-right
                x = region.width - margin
                y = margin

            # Set position using matrix_basis
            self.apply_scale_gizmo.matrix_basis[0][3] = x
            self.apply_scale_gizmo.matrix_basis[1][3] = y

        except Exception as e:
            print(f"Error positioning gizmo: {e}")

def register():
    """Register the extension"""
    try:
        bpy.utils.register_class(ApplyScaleOperator)
        bpy.utils.register_class(ApplyScaleAddonPreferences)
        bpy.utils.register_class(ApplyScaleGizmoGroup)
        print("Apply Scale Extension registered successfully")
    except Exception as e:
        print(f"Error registering Apply Scale Extension: {e}")

def unregister():
    """Unregister the extension"""
    try:
        bpy.utils.unregister_class(ApplyScaleGizmoGroup)
        bpy.utils.unregister_class(ApplyScaleAddonPreferences)
        bpy.utils.unregister_class(ApplyScaleOperator)
        print("Apply Scale Extension unregistered successfully")
    except Exception as e:
        print(f"Error unregistering Apply Scale Extension: {e}")

if __name__ == "__main__":
    register()