bl_info = {
    "name": "Apply Scale",
    "author": "Control",
    "version": (1, 0, 1),
    "blender": (3, 0, 0),
    "location": "3D Viewport",
    "description": "Shows a button to apply scale when object scale is not (1,1,1)",
    "category": "Object",
}

import bpy
from bpy.types import GizmoGroup

# Global variables
gizmo_group_registered = False

class ApplyScaleOperator(bpy.types.Operator):
    """Apply scale to selected object"""
    bl_idname = "object.apply_scale_addon"
    bl_label = "Apply Scale"
    bl_options = {'REGISTER', 'UNDO'}

    def execute(self, context):
        obj = context.active_object
        if obj and obj.select_get():
            # Store current mode
            current_mode = context.mode

            # Switch to object mode if in edit mode
            if current_mode == 'EDIT_MESH':
                bpy.ops.object.mode_set(mode='OBJECT')

            # Apply scale
            bpy.ops.object.transform_apply(location=False, rotation=False, scale=True)

            # Return to original mode
            if current_mode == 'EDIT_MESH':
                bpy.ops.object.mode_set(mode='EDIT')

        return {'FINISHED'}

def has_non_uniform_scale(obj):
    """Check if object has non-uniform scale (not 1,1,1)"""
    if not obj:
        return False

    scale = obj.scale
    tolerance = 0.0001
    return not (abs(scale.x - 1.0) < tolerance and
                abs(scale.y - 1.0) < tolerance and
                abs(scale.z - 1.0) < tolerance)

def should_show_gizmo(context : bpy.types.Context):
    """Check if gizmo should be shown"""
    obj = context.active_object
    return obj and obj.select_get() and has_non_uniform_scale(obj)

class ApplyScaleGizmoGroup(GizmoGroup):
    """Gizmo group for the apply scale button"""
    bl_idname = "VIEW3D_GGT_apply_scale"
    bl_label = "Apply Scale Gizmo"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'WINDOW'
    bl_options = {'PERSISTENT'}

    @classmethod
    def poll(cls, context):
        """Only show gizmo when object has non-uniform scale"""
        return should_show_gizmo(context)

    def setup(self, context):
        """Setup the gizmo"""
        # Create a 2D button gizmo
        gz = self.gizmos.new("GIZMO_GT_button_2d")
        gz.draw_style = 'BOX'
        gz.color = (0.2, 0.4, 0.8)
        gz.alpha = 0.7
        gz.color_highlight = (0.3, 0.6, 1.0)
        gz.alpha_highlight = 0.8
        gz.scale_basis = 80  # size in pixels

        # Set up the operator to call when clicked
        gz.target_set_operator("object.apply_scale_addon")

        self.gizmo = gz

    def draw_prepare(self, context):
        """Position the gizmo in the viewport"""
        region = context.region
        if not region:
            return

        width = region.width
        height = region.height

        # Position the gizmo in bottom-right corner (100px from edges)
        margin = 100
        x = width - margin
        y = margin

        self.gizmo.matrix_basis.translation = (x, y, 0)


def register():
    """Register the addon"""
    global gizmo_group_registered

    bpy.utils.register_class(ApplyScaleOperator)
    bpy.utils.register_class(ApplyScaleGizmoGroup)

    gizmo_group_registered = True

def unregister():
    """Unregister the addon"""
    global gizmo_group_registered

    # Unregister classes
    if gizmo_group_registered:
        bpy.utils.unregister_class(ApplyScaleGizmoGroup)
        bpy.utils.unregister_class(ApplyScaleOperator)
        gizmo_group_registered = False

if __name__ == "__main__":
    register()