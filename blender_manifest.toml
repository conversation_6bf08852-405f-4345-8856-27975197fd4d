schema_version = "1.0.0"

id = "apply_scale_addon"
version = "1.0.1"
name = "Apply Scale Addon"
tagline = "Shows a button to apply scale when object scale is not (1,1,1)"
maintainer = "Control <<EMAIL>>"
type = "add-on"

# Optional list of supported Blender versions
blender_version_min = "4.2.0"

# License conforming to https://spdx.org/licenses/ (use "SPDX:GPL-2.0-or-later", "SPDX:GPL-3.0-or-later", or "SPDX:MIT")
license = [
  "SPDX:GPL-3.0-or-later",
]

# Optional list of supported platforms
platforms = ["windows-x64", "macos-x64", "macos-arm64", "linux-x64"]

# Optional: extension website
# website = "https://extensions.blender.org/add-ons/apply-scale-addon/"

# Optional: extension tags
tags = [
  "3D View",
  "Object",
  "Transform",
  "User Interface",
]

[permissions]
# No special permissions needed for this extension
