# Apply Scale Extension for Blender
## 🚀 **Essential Tool for Professional 3D Workflows**

**Stop wasting time hunting through menus!** This extension solves one of the most common and frustrating problems in Blender: dealing with non-uniform object scales that break your workflow.

### ⚠️ **Why This Extension is Critical for Your Projects**

**Non-uniform scales (anything other than 1,1,1) cause serious problems:**
- ❌ **Broken modifiers** - Array, Mirror, and Solidify modifiers behave unpredictably
- ❌ **Incorrect measurements** - Your objects don't match real-world dimensions
- ❌ **Export failures** - Game engines and 3D printing software reject scaled objects
- ❌ **Animation issues** - Keyframes and constraints produce unexpected results
- ❌ **Texture distortion** - UV mapping and procedural textures stretch incorrectly
- ❌ **Physics simulation errors** - Rigid body and cloth simulations fail

### ✅ **What This Extension Does for You**

**Instantly identifies and fixes scale problems** with a smart, context-aware button that appears exactly when you need it:

- 🎯 **Zero-click detection** - Automatically appears when ANY selected object has problematic scale
- ⚡ **One-click solution** - Apply scale to ALL selected objects instantly
- 🧠 **Smart hierarchy handling** - Processes parent objects first to prevent child rescaling
- 🛡️ **Safe for all object types** - Handles meshes, lights, cameras, and special objects correctly
- 🎨 **Non-intrusive UI** - Clean gizmo that only appears when needed
- ⚙️ **Fully customizable** - Position, size, colors, and behavior all adjustable

### **💼 Perfect For:**
- **🎮 Game Development** - Clean geometry for engine export
- **🏭 Product Design** - Accurate measurements for manufacturing
- **🎬 Animation Studios** - Reliable modifier and constraint behavior
- **🖨️ 3D Printing** - Proper scale for physical output
- **📐 Architectural Visualization** - Real-world dimension accuracy

## 📦 **Quick Installation**

### 🎯 **Method 1: Extension Manager (Recommended - Blender 4.2+)**
1. **Download** this repository as ZIP or clone it
2. **Open Blender** → `Edit > Preferences > Extensions`
3. **Click** the dropdown arrow next to `+` → `Install from Disk...`
4. **Select** the entire folder containing `blender_manifest.toml`
5. **Done!** Extension auto-enables and is ready to use

### 🔧 **Method 2: Manual Installation**
1. **Copy** this folder to your Blender extensions directory:
   - **Windows**: `%APPDATA%\Blender Foundation\Blender\4.2\extensions\user_default\`
   - **macOS**: `~/Library/Application Support/Blender/4.2/extensions/user_default/`
   - **Linux**: `~/.config/blender/4.2/extensions/user_default/`
2. **Restart** Blender
3. **Verify** in `Edit > Preferences > Extensions`

### 🔄 **Legacy Addon (Blender < 4.2)**
1. **Go to** `Edit > Preferences > Add-ons`
2. **Click** `Install...` → select `__init__.py`
3. **Enable** by checking the box

---

## 🎮 **How to Use**

### **It's Automatic!**
1. **Select** any object with problematic scale (not 1,1,1)
2. **See** the red gizmo button appear in your chosen corner
3. **Click** once to fix ALL selected objects
4. **Continue** working - the button disappears when not needed

### **Pro Tips:**
- 💡 **Multi-select** objects to fix them all at once
- 💡 **Works in Edit Mode** - no need to switch modes
- 💡 **Handles hierarchies** - parents are processed first automatically
- 💡 **Customizable** - Change position, size, and colors in preferences

---

## ⚙️ **Comprehensive Customization**

### 🎨 **Visual Preferences**
Access full customization via `Edit > Preferences > Add-ons > Apply Scale`:

- **📏 Size Control**: 16-256 pixels (default: 64px)
- **📍 Position**: Choose any corner + custom X/Y margins
- **🎨 Colors**: Full RGBA control for normal and hover states
- **👁️ Transparency**: Adjustable alpha for subtle or bold appearance

### 🔧 **Default Settings**
- **Position**: Bottom-right corner, 40px margins
- **Size**: 64×64 pixels
- **Colors**: Professional red theme with hover feedback
- **Icon**: Fullscreen expand icon for clear visual meaning

---

## 🛠️ **Technical Excellence**

### **Smart Architecture**
- **🧠 Intelligent Detection**: Uses Blender's GizmoGroup system for optimal performance
- **⚡ Real-time Updates**: Monitors selection changes automatically
- **🔄 Mode Preservation**: Maintains your current editing mode
- **🏗️ Hierarchy Aware**: Processes parent-child relationships correctly
- **🛡️ Error Handling**: Robust error recovery and user feedback

### **Object Type Support**
- **✅ Full Support**: Meshes, curves, surfaces, meta objects, fonts, armatures, lattices, grease pencil
- **✅ Special Handling**: Lights, cameras, speakers, probes (direct scale reset)
- **✅ Hierarchy Safe**: Parent objects processed first to prevent child rescaling

### **Performance Optimized**
- **Zero overhead** when no objects need scale application
- **Minimal UI footprint** - only appears when needed
- **Efficient batch processing** for multiple objects
- **Smart tolerance checking** (0.0001 precision)

---

## 🤝 **Professional Use & Support**

### **📄 License**
- **GPL 3.0+** - Professional and commercial use welcome
- **Open Source** - Modify and distribute freely
- **No Restrictions** - Use in any project type

---

**⭐ Save hours of frustration - Install now and never hunt for the Apply Scale button again!**
