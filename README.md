# Apply Scale Addon for Blender

A Blender addon that automatically displays a button in the 3D viewport when you select an object with non-uniform scale (not 1,1,1). The button allows you to quickly apply the scale transformation to make it uniform.

## Features

- **Automatic Detection**: The addon automatically detects when you select an object with non-uniform scale
- **Viewport Button**: Shows a clickable button in the bottom-right corner of the 3D viewport (100px from edges)
- **Hover Effects**: <PERSON><PERSON> changes color when you hover over it
- **Mode Support**: Works in both Object Mode and Edit Mode
- **Smart Visibility**: <PERSON><PERSON> only appears when needed and disappears when:
  - Object scale becomes (1,1,1)
  - No object is selected
  - 3D viewport is closed
- **Visual Feedback**: Includes a scale icon (arrows pointing outward)

## Installation

1. Download or clone this repository
2. In Blender, go to `Edit > Preferences > Add-ons`
3. Click `Install...` and select the `__init__.py` file from this folder
4. Enable the addon by checking the box next to "Apply Scale Addon"

## Usage

1. Select any object in your scene that has a scale other than (1,1,1)
2. A blue button will appear in the bottom-right corner of the 3D viewport
3. Click the button to apply the scale transformation
4. The button will disappear once the scale is applied

## Technical Details

### Button Specifications
- **Size**: 80px × 80px
- **Position**: 100px from right edge, 100px from bottom edge
- **Style**: Box-style gizmo with built-in hover effects
- **Colors**:
  - Normal: Dark blue (0.2, 0.4, 0.8, 0.7)
  - Hover: Light blue (0.3, 0.6, 1.0, 0.8)

### How It Works

The addon uses Blender's GizmoGroup system to:
1. Monitor selection changes via `depsgraph_update_post` handler
2. Check if selected objects have non-uniform scale using the `poll()` method
3. Automatically show/hide the gizmo based on selection state
4. Handle button clicks through the gizmo's built-in operator target system
5. Apply scale transformation while preserving the current mode

### Code Structure

- `ApplyScaleOperator`: Handles the scale application logic
- `ApplyScaleGizmoGroup`: GizmoGroup that manages the button display and interaction
- `should_show_gizmo()`: Logic for determining when to show the gizmo
- `check_and_update_gizmo()`: Forces viewport updates when needed
- Event handlers for selection changes and viewport updates

## Customization

You can easily customize the addon by modifying these values in the `ApplyScaleGizmoGroup.setup()` method:

```python
# In the setup() method:
gz.scale_basis = 80                    # Button size in pixels
gz.color = (0.2, 0.4, 0.8)           # Normal state color (RGB)
gz.alpha = 0.7                        # Normal state alpha
gz.color_highlight = (0.3, 0.6, 1.0) # Hover state color (RGB)
gz.alpha_highlight = 0.8              # Hover state alpha

# In the draw_prepare() method:
margin = 100                          # Distance from viewport edges
```

## Requirements

- Blender 3.0 or higher
- No external dependencies

## License

This addon is provided as-is for educational and practical use. Feel free to modify and distribute according to your needs.
